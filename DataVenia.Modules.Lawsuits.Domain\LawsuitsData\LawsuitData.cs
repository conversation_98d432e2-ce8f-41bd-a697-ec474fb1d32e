﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.LawsuitResponsibles;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using Error = FluentResults.Error;
using FolderDomain = DataVenia.Modules.Lawsuits.Domain.Folder.Folder;
using LawsuitPartyDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitParties.LawsuitParty;

namespace DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
public sealed class LawsuitData : Entity
{
    private readonly List<LawsuitPartyDomain> _lawsuitParties = [];
    private readonly List<LawsuitResponsible> _responsibles = [];
    private readonly List<int> _topicIds = [];

    private LawsuitData() { }

    public IReadOnlyCollection<LawsuitPartyDomain> LawsuitParties => _lawsuitParties.AsReadOnly();
    public IReadOnlyCollection<LawsuitResponsible> Responsibles => _responsibles.AsReadOnly();
    public string? Title { get; private set; }
    public Guid Id { get; private set; }
    public string Cnj { get; private set; }
    public Guid LawsuitId { get; private set; }
    public Lawsuit Lawsuit { get; private set; }
    public Guid? FolderId { get; private set; }
    public FolderDomain Folder { get; private set; }
    public string? LegalInstanceId { get; private set; }
    public string? LawsuitStatusId { get; private set; }
    public IReadOnlyCollection<int> TopicIds => _topicIds.AsReadOnly(); // Assuntos
    public string? JudgingOrganId { get; private set; }
    public decimal? CauseValue { get; private set; }
    public decimal? ConvictionValue { get; private set; }
    public string? JudgingOrganHref { get; private set; }
    public string? Description { get; private set; }
    public string? Observations { get; private set; }
    public string? Access { get; private set; }
    public Guid? EvolvedFromCaseId { get; private set; }
    public Guid? GroupingCaseId { get; private set; }

    // temporary property. Will be removed when we refactor the code to support user creating multiple instances per lawsuit
    public bool IsInstanceCreatedByUser { get; private set; }
    public DateTime CreatedAt { get; private set; }

    public static FluentResults.Result<LawsuitData> Create(
            string? title,
            string cnj,
            Guid lawsuitId,
            Guid? folderId,
            string? legalInstanceId,
            string? lawsuitStatusId,
            List<int> topicIds,
            string? judgingOrganId,
            decimal? causeValue,
            decimal? convictionValue,
            string? judgingOrganHref,
            string? description,
            string? observations,
            string? access,
            List<Guid> responsiblesIds,
            Guid? evolvedFromCaseId,
            Guid? groupingCaseId,
            bool isInstanceCreatedByUser
            )
    {
        if (responsiblesIds.Count == 0)
            return FluentResults.Result.Fail<LawsuitData>(new Error("Responsible.Required").WithMetadata("StatusCode", 400));

        var newLawsuitData =  new LawsuitData
        {
            Title = title,
            Cnj = cnj,
            Id = Guid.NewGuid(),
            LawsuitId = lawsuitId,
            FolderId = folderId,
            LegalInstanceId = legalInstanceId,
            LawsuitStatusId = lawsuitStatusId,
            JudgingOrganId = judgingOrganId,
            CauseValue = causeValue,
            ConvictionValue = convictionValue,
            JudgingOrganHref = judgingOrganHref,
            Description = description,
            Observations = observations,
            Access = access,
            CreatedAt = DateTime.UtcNow,
            EvolvedFromCaseId = evolvedFromCaseId,
            GroupingCaseId = groupingCaseId,
            IsInstanceCreatedByUser = isInstanceCreatedByUser
        };

        newLawsuitData._topicIds.AddRange(topicIds);

        var lawsuitResponsibles = responsiblesIds.Select(x => LawsuitResponsible.Create(lawsuitId, x)).ToList();

        newLawsuitData._responsibles.AddRange(lawsuitResponsibles);

        return newLawsuitData;
    }

    public static FluentResults.Result<LawsuitData> CreateByCnj(string cnj, string? title, Guid lawsuitId, string? description, List<Guid> responsiblesIds, Guid? evolvedFromCaseId = null, Guid? groupingCaseId = null)
    {
        var newLawsuitData = new LawsuitData
        {
            Title = title,
            Cnj = cnj,
            Id = Guid.NewGuid(),
            LawsuitId = lawsuitId,
            Description = description,
            CreatedAt = DateTime.UtcNow,
            EvolvedFromCaseId = evolvedFromCaseId,
            GroupingCaseId = groupingCaseId,
            IsInstanceCreatedByUser = true
        };

        if (responsiblesIds.Count == 0)
            return FluentResults.Result.Fail<LawsuitData>(new Error("Responsible.Required").WithMetadata("StatusCode", 400));

        var lawsuitResponsibles = responsiblesIds.Select(x => LawsuitResponsible.Create(lawsuitId, x)).ToList();

        newLawsuitData._responsibles.AddRange(lawsuitResponsibles);

        return newLawsuitData;
    }

    public void CheckAndUpdateCoverData(Lawsuit lawsuit, string instanceId, string judgingOrganId, decimal causeValue, List<string> topicIds)
    {
        var divergentFields = new Dictionary<string, (string current, string eventValue)>();

        // Check JudgingOrganId
        if (!string.IsNullOrEmpty(judgingOrganId) && !string.Equals(JudgingOrganId, judgingOrganId))
        {
            if (string.IsNullOrEmpty(JudgingOrganId))
            {
                // If our value is empty, update it
                JudgingOrganId = judgingOrganId;
            }
            else
            {
                // If our value is different, add to divergent fields
                divergentFields["JudgingOrganId"] = (JudgingOrganId ?? string.Empty, judgingOrganId);
            }
        }

        // Check CauseValue
        if (causeValue > 0 && (!CauseValue.HasValue || CauseValue != causeValue))
        {
            if (!CauseValue.HasValue || CauseValue == 0)
            {
                // If our value is empty, update it
                CauseValue = causeValue;
            }
            else
            {
                // If our value is different, add to divergent fields
                divergentFields["CauseValue"] = (CauseValue?.ToString() ?? string.Empty, causeValue.ToString());
            }
        }

        // Check TopicIds
        if (topicIds != null && topicIds.Any())
        {
            var numericTopicIds = new List<int>();

            // Try to convert string topic IDs to integers
            foreach (var topicIdStr in topicIds)
            {
                if (int.TryParse(topicIdStr, out int topicId))
                {
                    numericTopicIds.Add(topicId);
                }
            }

            if (numericTopicIds.Any())
            {
                // If we don't have any topics, add the ones from the event
                if (!_topicIds.Any())
                {
                    _topicIds.AddRange(numericTopicIds);
                }
                else
                {
                    // Check for differences
                    var currentTopicIds = _topicIds.ToHashSet();
                    var eventTopicIds = numericTopicIds.ToHashSet();

                    // Find topics in the event that are not in our current data
                    var missingTopics = eventTopicIds.Except(currentTopicIds).ToList();

                    if (missingTopics.Any())
                    {
                        // Add to divergent fields
                        var currentTopicsStr = string.Join(", ", _topicIds);
                        var eventTopicsStr = string.Join(", ", numericTopicIds);
                        divergentFields["TopicIds"] = (currentTopicsStr, eventTopicsStr);
                    }
                }
            }
        }

        // If we have any divergent fields, add them to the lawsuit's divergence
        if (divergentFields.Any())
        {
            foreach (var field in divergentFields)
            {
                lawsuit.AddOrUpdateDataDivergence(DataDivergence.Create(instanceId, new Dictionary<string, DivergentField>{ { field.Key, new DivergentField(field.Value.current, field.Value.eventValue)}}));
            }
        }
    }
}
