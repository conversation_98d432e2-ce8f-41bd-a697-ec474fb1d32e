﻿using DataVenia.Common.Domain;
using System.Text.Json;

namespace DataVenia.Modules.Lawsuits.Domain.Lawsuits;

public sealed class DataDivergence : Entity
{
    private readonly Dictionary<string, DivergentField> _fields = new();

    public Guid Id { get; private set; }
    public string InstanceId { get; private set; }
    public string FieldsJson { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public bool IsAnalyzed { get; private set; }
    public DateTime? AnalyzedAt { get; private set; }
    public string? AnalyzedBy { get; private set; }
    public bool? WasAccepted { get; private set; }

    public IReadOnlyDictionary<string, DivergentField> Fields
    {
        get
        {
            if (_fields.Count == 0 && !string.IsNullOrEmpty(FieldsJson))
            {
                var deserializedFields = JsonSerializer.Deserialize<Dictionary<string, DivergentField>>(FieldsJson);
                if (deserializedFields != null)
                {
                    foreach (var field in deserializedFields)
                    {
                        _fields[field.Key] = field.Value;
                    }
                }
            }
            return _fields.AsReadOnly();
        }
    }

    private DataDivergence() { }

    public static DataDivergence Create(string instanceId, Dictionary<string, DivergentField> fields)
    {
        var divergence = new DataDivergence
        {
            Id = Guid.CreateVersion7(),
            InstanceId = instanceId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = null,
            IsAnalyzed = false,
            WasAccepted = null
        };

        foreach (var field in fields)
        {
            divergence._fields[field.Key] = field.Value;
        }

        divergence.FieldsJson = JsonSerializer.Serialize(divergence._fields);
        return divergence;
    }

    public void AddOrUpdateField(string fieldName, string currentValue, string eventValue)
    {
        _fields[fieldName] = new DivergentField(currentValue, eventValue);
        FieldsJson = JsonSerializer.Serialize(_fields);
        UpdatedAt = DateTime.UtcNow;

        // Reset analysis status when fields change
        if (IsAnalyzed)
        {
            IsAnalyzed = false;
            AnalyzedAt = null;
            AnalyzedBy = null;
            WasAccepted = null;
        }
    }

    public void MarkAsAnalyzed(string analyzedBy, bool wasAccepted)
    {
        IsAnalyzed = true;
        AnalyzedAt = DateTime.UtcNow;
        AnalyzedBy = analyzedBy;
        WasAccepted = wasAccepted;
    }

    public void UpdateLastSeenTime()
    {
        UpdatedAt = DateTime.UtcNow;
    }

    public bool HasField(string fieldName)
    {
        return Fields.ContainsKey(fieldName);
    }

    public bool HasSameEventValue(string fieldName, string eventValue)
    {
        return Fields.TryGetValue(fieldName, out var field) && field.EventValue == eventValue;
    }

    public bool HasAnyFieldWithDifferentEventValue(Dictionary<string, string> newEventValues)
    {
        foreach (var newValue in newEventValues)
        {
            if (!HasSameEventValue(newValue.Key, newValue.Value))
            {
                return true;
            }
        }
        return false;
    }
}

public sealed record DivergentField(string CurrentValue, string EventValue);
